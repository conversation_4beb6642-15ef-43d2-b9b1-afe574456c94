#!/usr/bin/env python3
"""
調試地圖元素 - 專門檢查農業站標記
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service


def setup_chrome_driver():
    """設置Chrome WebDriver"""
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--window-size=1920,1080')
    
    try:
        if os.path.exists("chromedriver.exe"):
            service = Service(executable_path="./chromedriver.exe")
            driver = webdriver.Chrome(service=service, options=chrome_options)
        else:
            driver = webdriver.Chrome(options=chrome_options)
        
        driver.implicitly_wait(10)
        return driver
    except Exception as e:
        print(f"Chrome WebDriver 設置失敗: {e}")
        return None


def debug_map_elements():
    """調試地圖元素"""
    print("=" * 50)
    print("調試地圖上的農業站標記")
    print("=" * 50)
    
    driver = setup_chrome_driver()
    if not driver:
        return
    
    try:
        # 1. 訪問網站
        print("1. 訪問CODiS網站...")
        driver.get("https://codis.cwa.gov.tw/StationData")
        WebDriverWait(driver, 30).until(EC.presence_of_element_located((By.TAG_NAME, "body")))
        time.sleep(5)
        
        # 2. 設置測站篩選
        print("2. 設置測站篩選...")
        
        # 取消署屬有人站
        cwb_checkbox = driver.find_element(By.ID, "cwb")
        if cwb_checkbox.is_selected():
            driver.execute_script("arguments[0].click();", cwb_checkbox)
            print("   ✓ 已取消勾選「署屬有人站」")
        
        # 確保農業站勾選
        agr_checkbox = driver.find_element(By.ID, "agr")
        if not agr_checkbox.is_selected():
            driver.execute_script("arguments[0].click();", agr_checkbox)
            print("   ✓ 已勾選「農業站」")
        else:
            print("   ✓ 「農業站」已經勾選")
        
        # 3. 等待地圖更新並分析元素
        print("3. 等待地圖更新...")
        for i in range(10):
            print(f"   等待 {i+1}/10 秒...")
            time.sleep(1)
        
        print("4. 分析地圖元素...")
        
        # 檢查所有可能的地圖元素
        element_types = [
            ("SVG元素", "//svg"),
            ("SVG圓圈", "//*[name()='circle']"),
            ("有填充的圓圈", "//circle[@fill]"),
            ("綠色圓圈", "//*[name()='circle' and contains(@fill, 'green')]"),
            ("所有圓圈", "//circle"),
            ("地圖標記", "//div[contains(@class, 'marker')]"),
            ("站點標記", "//div[contains(@class, 'station')]"),
            ("圖片標記", "//img[contains(@src, 'marker')]"),
            ("可點擊元素", "//*[@onclick]"),
            ("有事件的元素", "//*[@onmousedown or @onmouseup or @onclick]")
        ]
        
        for desc, selector in element_types:
            try:
                elements = driver.find_elements(By.XPATH, selector)
                print(f"\n   {desc}: 找到 {len(elements)} 個")
                
                # 顯示前5個元素的詳細資訊
                for i, elem in enumerate(elements[:5]):
                    try:
                        tag = elem.tag_name
                        attrs = {}
                        
                        # 獲取常見屬性
                        common_attrs = ['id', 'class', 'fill', 'stroke', 'r', 'cx', 'cy', 'style', 'onclick', 'src']
                        for attr in common_attrs:
                            value = elem.get_attribute(attr)
                            if value:
                                attrs[attr] = value[:50] + ('...' if len(value) > 50 else '')
                        
                        print(f"     {i+1}. <{tag}> {attrs}")
                        
                        # 如果是圓圈，檢查是否可能是農業站
                        if tag == 'circle' and 'fill' in attrs:
                            fill_color = attrs['fill'].lower()
                            if 'green' in fill_color or '#00' in fill_color:
                                print(f"       *** 可能是農業站標記！***")
                                
                    except Exception as e:
                        print(f"     {i+1}. 無法獲取屬性: {e}")
                        
            except Exception as e:
                print(f"   {desc}: 查詢失敗 - {e}")
        
        # 5. 嘗試手動點擊測試
        print("\n5. 手動點擊測試...")
        print("   請在瀏覽器中手動點擊一個綠色圈圈")
        print("   觀察是否出現站點資訊面板")
        
        input("   點擊完成後按Enter繼續...")
        
        # 6. 檢查點擊後的變化
        print("6. 檢查點擊後的頁面變化...")
        
        # 尋找可能的彈出面板
        panel_selectors = [
            ("彈出視窗", "//div[contains(@class, 'popup')]"),
            ("模態框", "//div[contains(@class, 'modal')]"),
            ("資訊面板", "//div[contains(@class, 'info')]"),
            ("對話框", "//div[contains(@class, 'dialog')]"),
            ("包含月報表的元素", "//*[contains(text(), '月報表')]"),
            ("包含CSV的元素", "//*[contains(text(), 'CSV')]"),
            ("選擇器", "//select"),
            ("新按鈕", "//button[not(@id='cwb' or @id='agr' or @id='auto_C1' or @id='auto_C0')]")
        ]
        
        for desc, selector in panel_selectors:
            try:
                elements = driver.find_elements(By.XPATH, selector)
                if elements:
                    print(f"   {desc}: 找到 {len(elements)} 個")
                    for i, elem in enumerate(elements[:3]):
                        try:
                            text = elem.text.strip()[:100]
                            tag = elem.tag_name
                            class_attr = elem.get_attribute('class') or ''
                            print(f"     {i+1}. <{tag}> class='{class_attr}' text='{text}'")
                        except:
                            pass
            except:
                continue
        
        print("\n調試完成！")
        
    except Exception as e:
        print(f"調試失敗: {e}")
    finally:
        input("按Enter鍵關閉瀏覽器...")
        driver.quit()


if __name__ == "__main__":
    debug_map_elements()
