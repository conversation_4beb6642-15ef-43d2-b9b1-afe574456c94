#!/usr/bin/env python3
"""
CODiS 爬蟲詳細測試程式
模擬完整的操作流程
"""

import os
import time
from datetime import datetime, timedelta
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import NoSuchElementException, TimeoutException


def setup_chrome_driver(headless=True):
    """設置Chrome WebDriver"""
    print("正在設置Chrome WebDriver...")
    
    chrome_options = Options()
    if headless:
        chrome_options.add_argument('--headless')
        print("使用無頭模式")
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--window-size=1920,1080')
    chrome_options.add_argument('--disable-blink-features=AutomationControlled')
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    # 設置下載目錄
    output_dir = "agricultural_data"
    os.makedirs(output_dir, exist_ok=True)
    
    prefs = {
        "download.default_directory": os.path.abspath(output_dir),
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    chrome_options.add_experimental_option("prefs", prefs)
    
    try:
        if os.path.exists("chromedriver.exe"):
            service = Service(executable_path="./chromedriver.exe")
            driver = webdriver.Chrome(service=service, options=chrome_options)
        else:
            driver = webdriver.Chrome(options=chrome_options)
        
        driver.implicitly_wait(10)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        print("Chrome WebDriver 設置成功")
        return driver
    except Exception as e:
        print(f"Chrome WebDriver 設置失敗: {e}")
        return None


def test_full_workflow():
    """測試完整的工作流程"""
    print("=" * 50)
    print("測試 CODiS 完整工作流程")
    print("=" * 50)
    
    driver = setup_chrome_driver(headless=True)
    if not driver:
        return False
    
    try:
        # 1. 訪問網站
        print("1. 正在訪問CODiS網站...")
        driver.get("https://codis.cwa.gov.tw/StationData")
        WebDriverWait(driver, 30).until(EC.presence_of_element_located((By.TAG_NAME, "body")))
        time.sleep(5)
        print(f"   頁面標題: {driver.title}")
        
        # 2. 選擇農業站
        print("2. 正在選擇農業站...")
        try:
            agr_checkbox = driver.find_element(By.ID, "agr")
            if not agr_checkbox.is_selected():
                driver.execute_script("arguments[0].click();", agr_checkbox)
                print("   ✓ 農業站已選擇")
                time.sleep(2)
            else:
                print("   ✓ 農業站已經被選擇")
        except Exception as e:
            print(f"   ✗ 選擇農業站失敗: {e}")
            return False
        
        # 3. 檢查頁面變化
        print("3. 檢查選擇農業站後的頁面變化...")
        time.sleep(3)
        
        # 重新檢查所有元素
        buttons = driver.find_elements(By.TAG_NAME, "button")
        print(f"   現在找到 {len(buttons)} 個button:")
        
        download_buttons = []
        for i, btn in enumerate(buttons):
            try:
                text = btn.text.strip()
                class_attr = btn.get_attribute("class") or ""
                onclick = btn.get_attribute("onclick") or ""
                
                if text:  # 只顯示有文字的按鈕
                    print(f"     {i+1}. '{text}' (class: {class_attr})")
                    
                    if any(keyword in text.lower() for keyword in ['csv', '下載', 'download', '匯出']):
                        download_buttons.append(btn)
                        print(f"       *** 可能是下載按鈕 ***")
                        
            except Exception as e:
                continue
        
        # 4. 尋找其他可能的下載元素
        print("4. 尋找其他下載相關元素...")
        
        # 檢查所有連結
        links = driver.find_elements(By.TAG_NAME, "a")
        print(f"   找到 {len(links)} 個連結:")
        for i, link in enumerate(links[:10]):
            try:
                text = link.text.strip()
                href = link.get_attribute("href") or ""
                if text and any(keyword in text.lower() for keyword in ['csv', '下載', 'download', '匯出']):
                    print(f"     {i+1}. '{text}' -> {href}")
                    download_buttons.append(link)
            except:
                continue
        
        # 檢查所有input元素
        inputs = driver.find_elements(By.TAG_NAME, "input")
        print(f"   找到 {len(inputs)} 個input:")
        for i, inp in enumerate(inputs):
            try:
                input_type = inp.get_attribute("type") or ""
                value = inp.get_attribute("value") or ""
                if input_type == "submit" or input_type == "button":
                    if any(keyword in value.lower() for keyword in ['csv', '下載', 'download', '匯出']):
                        print(f"     {i+1}. type='{input_type}', value='{value}'")
                        download_buttons.append(inp)
            except:
                continue
        
        # 5. 嘗試點擊下載按鈕
        if download_buttons:
            print(f"5. 找到 {len(download_buttons)} 個可能的下載按鈕，嘗試點擊...")
            for i, btn in enumerate(download_buttons):
                try:
                    print(f"   嘗試點擊按鈕 {i+1}...")
                    driver.execute_script("arguments[0].click();", btn)
                    time.sleep(3)
                    print(f"   ✓ 按鈕 {i+1} 點擊成功")
                    break
                except Exception as e:
                    print(f"   ✗ 按鈕 {i+1} 點擊失敗: {e}")
        else:
            print("5. 未找到明顯的下載按鈕")
            
            # 嘗試尋找可能需要額外步驟的元素
            print("   嘗試尋找其他可能的操作...")
            
            # 檢查是否有日期選擇器
            date_inputs = driver.find_elements(By.XPATH, "//input[@type='date' or @type='text' or contains(@class, 'date')]")
            if date_inputs:
                print(f"   找到 {len(date_inputs)} 個日期相關輸入框")
                
            # 檢查是否有查詢按鈕
            query_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), '查詢') or contains(text(), '搜尋') or contains(text(), 'search')]")
            if query_buttons:
                print(f"   找到 {len(query_buttons)} 個查詢按鈕")
                for btn in query_buttons:
                    try:
                        print(f"     查詢按鈕: '{btn.text.strip()}'")
                    except:
                        pass
        
        # 6. 檢查下載目錄
        print("6. 檢查下載目錄...")
        output_dir = "agricultural_data"
        if os.path.exists(output_dir):
            files = os.listdir(output_dir)
            csv_files = [f for f in files if f.endswith('.csv')]
            if csv_files:
                print(f"   ✓ 找到 {len(csv_files)} 個CSV檔案:")
                for f in csv_files:
                    print(f"     - {f}")
            else:
                print("   ✗ 未找到CSV檔案")
        
        print("\n測試完成！")
        return True
        
    except Exception as e:
        print(f"測試失敗: {e}")
        return False
    finally:
        driver.quit()


if __name__ == "__main__":
    test_full_workflow()
