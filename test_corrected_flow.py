#!/usr/bin/env python3
"""
測試修正後的爬蟲流程
"""

import os
import time
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service


def setup_chrome_driver():
    """設置Chrome WebDriver"""
    print("正在設置Chrome WebDriver...")
    
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--window-size=1920,1080')
    
    # 設置下載目錄
    output_dir = "agricultural_data"
    os.makedirs(output_dir, exist_ok=True)
    
    prefs = {
        "download.default_directory": os.path.abspath(output_dir),
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    chrome_options.add_experimental_option("prefs", prefs)
    
    try:
        if os.path.exists("chromedriver.exe"):
            service = Service(executable_path="./chromedriver.exe")
            driver = webdriver.Chrome(service=service, options=chrome_options)
        else:
            driver = webdriver.Chrome(options=chrome_options)
        
        driver.implicitly_wait(10)
        print("Chrome WebDriver 設置成功")
        return driver
    except Exception as e:
        print(f"Chrome WebDriver 設置失敗: {e}")
        return None


def test_corrected_flow():
    """測試修正後的流程"""
    print("=" * 50)
    print("測試修正後的CODiS爬蟲流程")
    print("=" * 50)
    
    driver = setup_chrome_driver()
    if not driver:
        return False
    
    try:
        # 1. 訪問網站
        print("1. 訪問CODiS網站...")
        driver.get("https://codis.cwa.gov.tw/StationData")
        WebDriverWait(driver, 30).until(EC.presence_of_element_located((By.TAG_NAME, "body")))
        time.sleep(5)
        print("   ✓ 網站載入成功")
        
        # 2. 設置測站篩選
        print("2. 設置測站篩選...")
        
        # 取消署屬有人站
        cwb_checkbox = driver.find_element(By.ID, "cwb")
        if cwb_checkbox.is_selected():
            driver.execute_script("arguments[0].click();", cwb_checkbox)
            print("   ✓ 已取消勾選「署屬有人站」")
        
        # 確保農業站勾選
        agr_checkbox = driver.find_element(By.ID, "agr")
        if not agr_checkbox.is_selected():
            driver.execute_script("arguments[0].click();", agr_checkbox)
            print("   ✓ 已勾選「農業站」")
        else:
            print("   ✓ 「農業站」已經勾選")
        
        time.sleep(3)  # 等待地圖更新
        
        # 3. 觀察地圖變化
        print("3. 觀察地圖上的農業站標記...")
        print("   農業站勾選後，地圖上應該會顯示綠色圈圈")
        
        # 尋找可能的農業站標記
        selectors = [
            "//*[name()='circle']",  # 所有SVG圓圈
            "//div[contains(@class, 'marker')]",  # 標記
            "//circle[@fill]"  # 有填充色的圓圈
        ]
        
        for i, selector in enumerate(selectors):
            try:
                elements = driver.find_elements(By.XPATH, selector)
                if elements:
                    print(f"   找到 {len(elements)} 個可能的標記 (選擇器 {i+1})")
                    
                    # 顯示前幾個元素的屬性
                    for j, elem in enumerate(elements[:5]):
                        try:
                            fill = elem.get_attribute('fill') or ''
                            class_attr = elem.get_attribute('class') or ''
                            style = elem.get_attribute('style') or ''
                            print(f"     標記 {j+1}: fill='{fill}', class='{class_attr}', style='{style[:50]}...'")
                        except:
                            pass
            except:
                continue
        
        # 4. 手動指導
        print("\n4. 手動操作指導:")
        print("   請在瀏覽器中：")
        print("   - 觀察地圖上是否出現綠色圈圈（農業站標記）")
        print("   - 點擊任意一個綠色圈圈")
        print("   - 查看是否彈出站點資訊面板")
        print("   - 在面板中選擇「月報表」")
        print("   - 設置年月（例如：2025/06）")
        print("   - 點擊「CSV下載」按鈕")
        
        input("\n按Enter鍵繼續觀察瀏覽器...")
        
        # 5. 檢查下載結果
        print("5. 檢查下載結果...")
        output_dir = "agricultural_data"
        if os.path.exists(output_dir):
            files = os.listdir(output_dir)
            csv_files = [f for f in files if f.endswith('.csv')]
            if csv_files:
                print(f"   ✓ 找到 {len(csv_files)} 個CSV檔案:")
                for f in csv_files:
                    file_path = os.path.join(output_dir, f)
                    file_size = os.path.getsize(file_path)
                    print(f"     - {f} ({file_size} bytes)")
                return True
            else:
                print("   ✗ 未找到CSV檔案")
        
        return False
        
    except Exception as e:
        print(f"測試失敗: {e}")
        return False
    finally:
        input("按Enter鍵關閉瀏覽器...")
        driver.quit()


if __name__ == "__main__":
    test_corrected_flow()
