# CODiS 農業站月報表資料爬蟲

從中央氣象署CODiS系統爬取過去3年的農業站月報表CSV資料。

## 快速開始

### 1. 安裝依賴
```bash
pip install selenium
```

### 2. 確保ChromeDriver可用
- 下載ChromeDriver: https://chromedriver.chromium.org/
- 將chromedriver.exe放到專案目錄或PATH中

### 3. 運行爬蟲
```bash
python codis_scraper.py
```

## 檔案說明

- `codis_scraper.py` - 主要爬蟲程式
- `requirements.txt` - Python套件依賴
- `agricultural_data/` - 下載的CSV資料儲存目錄

## 功能

- 自動選擇農業站
- 設定月報表資料類型
- 設定過去3年的時間範圍
- 下載CSV格式資料
- 支援有頭/無頭模式運行

## 手動下載方式

如果自動化失敗，可手動操作：

1. 開啟 https://codis.cwa.gov.tw/StationData
2. 勾選「農業站」
3. 選擇「月報表(逐日資料)」
4. 設定時間範圍為過去3年
5. 點擊「CSV下載」

## 注意事項

- 僅供研究學習使用
- 請遵守網站使用條款
- 避免過於頻繁的請求
