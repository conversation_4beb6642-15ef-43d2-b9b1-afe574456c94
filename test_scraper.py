#!/usr/bin/env python3
"""
CODiS 爬蟲測試程式
用於測試和調試爬蟲功能
"""

import os
import time
from datetime import datetime, timedelta
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import NoSuchElementException, TimeoutException


def setup_chrome_driver(headless=False):
    """設置Chrome WebDriver"""
    print("正在設置Chrome WebDriver...")

    chrome_options = Options()
    if headless:
        chrome_options.add_argument("--headless")
        print("使用無頭模式")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option("useAutomationExtension", False)

    # 設置下載目錄
    output_dir = "agricultural_data"
    os.makedirs(output_dir, exist_ok=True)

    prefs = {
        "download.default_directory": os.path.abspath(output_dir),
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True,
    }
    chrome_options.add_experimental_option("prefs", prefs)

    try:
        # 新版本Selenium使用Service
        from selenium.webdriver.chrome.service import Service

        if os.path.exists("chromedriver.exe"):
            service = Service(executable_path="./chromedriver.exe")
            driver = webdriver.Chrome(service=service, options=chrome_options)
        else:
            driver = webdriver.Chrome(options=chrome_options)

        driver.implicitly_wait(10)
        # 隱藏自動化標識
        driver.execute_script(
            "Object.defineProperty(navigator, 'webdriver', {get: () => undefined})"
        )
        print("Chrome WebDriver 設置成功")
        return driver
    except Exception as e:
        print(f"Chrome WebDriver 設置失敗: {e}")
        return None


def test_website_access():
    """測試網站訪問"""
    print("=" * 50)
    print("測試 CODiS 網站訪問")
    print("=" * 50)

    driver = setup_chrome_driver(headless=False)  # 使用有頭模式方便調試
    if not driver:
        return False

    try:
        print("正在訪問CODiS網站...")
        driver.get("https://codis.cwa.gov.tw/StationData")

        # 等待頁面載入
        WebDriverWait(driver, 30).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        time.sleep(5)

        print(f"頁面標題: {driver.title}")
        print(f"當前URL: {driver.current_url}")

        # 檢查頁面內容
        body_text = driver.find_element(By.TAG_NAME, "body").text
        print(f"頁面內容長度: {len(body_text)} 字符")

        # 尋找所有checkbox
        checkboxes = driver.find_elements(By.XPATH, "//input[@type='checkbox']")
        print(f"找到 {len(checkboxes)} 個checkbox:")

        for i, cb in enumerate(checkboxes[:15]):  # 顯示前15個
            try:
                value = cb.get_attribute("value") or ""
                id_attr = cb.get_attribute("id") or ""
                name_attr = cb.get_attribute("name") or ""
                class_attr = cb.get_attribute("class") or ""
                is_displayed = cb.is_displayed()
                is_enabled = cb.is_enabled()

                print(
                    f"  {i+1}. value='{value}', id='{id_attr}', name='{name_attr}', class='{class_attr}', displayed={is_displayed}, enabled={is_enabled}"
                )

                # 檢查是否包含農業相關字詞
                if "農業" in value or "農業" in id_attr or "農業" in name_attr:
                    print(f"    *** 可能是農業站選項 ***")

            except Exception as e:
                print(f"  {i+1}. 無法獲取屬性: {e}")

        # 尋找所有select元素
        selects = driver.find_elements(By.TAG_NAME, "select")
        print(f"\n找到 {len(selects)} 個select元素:")

        for i, sel in enumerate(selects[:10]):
            try:
                name_attr = sel.get_attribute("name") or ""
                id_attr = sel.get_attribute("id") or ""
                options = sel.find_elements(By.TAG_NAME, "option")
                print(
                    f"  {i+1}. name='{name_attr}', id='{id_attr}', options={len(options)}"
                )

                # 顯示選項
                for j, opt in enumerate(options[:5]):
                    opt_text = opt.text.strip()
                    opt_value = opt.get_attribute("value") or ""
                    print(f"    - {opt_text} (value='{opt_value}')")

            except Exception as e:
                print(f"  {i+1}. 無法獲取select信息: {e}")

        # 尋找所有button
        buttons = driver.find_elements(By.TAG_NAME, "button")
        print(f"\n找到 {len(buttons)} 個button:")

        for i, btn in enumerate(buttons[:10]):
            try:
                text = btn.text.strip()
                class_attr = btn.get_attribute("class") or ""
                onclick = btn.get_attribute("onclick") or ""
                print(
                    f"  {i+1}. text='{text}', class='{class_attr}', onclick='{onclick[:50]}...'"
                )

                if "CSV" in text or "下載" in text or "download" in class_attr.lower():
                    print(f"    *** 可能是下載按鈕 ***")

            except Exception as e:
                print(f"  {i+1}. 無法獲取button信息: {e}")

        print("\n測試完成！請檢查瀏覽器視窗以查看實際頁面")
        input("按Enter鍵繼續...")

        return True

    except Exception as e:
        print(f"測試失敗: {e}")
        return False
    finally:
        driver.quit()


if __name__ == "__main__":
    test_website_access()
