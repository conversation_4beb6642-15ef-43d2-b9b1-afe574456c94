#!/usr/bin/env python3
"""
CODiS 下載流程測試
專門測試下載功能
"""

import os
import time
from datetime import datetime, timedelta
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import NoSuchElementException, TimeoutException


def setup_chrome_driver(headless=False):
    """設置Chrome WebDriver"""
    print("正在設置Chrome WebDriver...")
    
    chrome_options = Options()
    if headless:
        chrome_options.add_argument('--headless')
        print("使用無頭模式")
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--window-size=1920,1080')
    chrome_options.add_argument('--disable-blink-features=AutomationControlled')
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    # 設置下載目錄
    output_dir = "agricultural_data"
    os.makedirs(output_dir, exist_ok=True)
    
    prefs = {
        "download.default_directory": os.path.abspath(output_dir),
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True
    }
    chrome_options.add_experimental_option("prefs", prefs)
    
    try:
        if os.path.exists("chromedriver.exe"):
            service = Service(executable_path="./chromedriver.exe")
            driver = webdriver.Chrome(service=service, options=chrome_options)
        else:
            driver = webdriver.Chrome(options=chrome_options)
        
        driver.implicitly_wait(10)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        print("Chrome WebDriver 設置成功")
        return driver
    except Exception as e:
        print(f"Chrome WebDriver 設置失敗: {e}")
        return None


def test_download_flow():
    """測試下載流程"""
    print("=" * 50)
    print("測試 CODiS 下載流程")
    print("=" * 50)
    
    driver = setup_chrome_driver(headless=False)  # 使用有頭模式方便觀察
    if not driver:
        return False
    
    try:
        # 1. 訪問網站
        print("1. 正在訪問CODiS網站...")
        driver.get("https://codis.cwa.gov.tw/StationData")
        WebDriverWait(driver, 30).until(EC.presence_of_element_located((By.TAG_NAME, "body")))
        time.sleep(5)
        
        # 2. 選擇農業站
        print("2. 正在選擇農業站...")
        agr_checkbox = driver.find_element(By.ID, "agr")
        if not agr_checkbox.is_selected():
            driver.execute_script("arguments[0].click();", agr_checkbox)
            print("   ✓ 農業站已選擇")
            time.sleep(3)
        
        # 3. 點擊「圖資查詢與下載」
        print("3. 尋找並點擊「圖資查詢與下載」...")
        try:
            download_link = driver.find_element(By.LINK_TEXT, "圖資查詢與下載")
            driver.execute_script("arguments[0].click();", download_link)
            print("   ✓ 已點擊「圖資查詢與下載」")
            time.sleep(5)
        except Exception as e:
            print(f"   ✗ 點擊失敗: {e}")
            # 嘗試其他方式
            try:
                download_link = driver.find_element(By.XPATH, "//a[contains(text(), '圖資查詢與下載')]")
                driver.execute_script("arguments[0].click();", download_link)
                print("   ✓ 已點擊「圖資查詢與下載」(備用方法)")
                time.sleep(5)
            except Exception as e2:
                print(f"   ✗ 備用方法也失敗: {e2}")
                return False
        
        # 4. 檢查頁面變化
        print("4. 檢查點擊後的頁面變化...")
        current_url = driver.current_url
        print(f"   當前URL: {current_url}")
        
        # 檢查是否有新的元素出現
        time.sleep(3)
        
        # 尋找所有可能的下載相關元素
        print("5. 尋找下載相關元素...")
        
        # 檢查所有按鈕
        buttons = driver.find_elements(By.TAG_NAME, "button")
        print(f"   找到 {len(buttons)} 個按鈕:")
        for i, btn in enumerate(buttons):
            try:
                text = btn.text.strip()
                if text and any(keyword in text for keyword in ['CSV', 'csv', '下載', '匯出', '查詢', '搜尋']):
                    print(f"     {i+1}. '{text}'")
            except:
                continue
        
        # 檢查所有輸入框和選擇器
        print("6. 檢查日期和選項設置...")
        
        # 尋找日期相關的輸入框
        date_inputs = driver.find_elements(By.XPATH, "//input[@type='date' or @type='text' or contains(@placeholder, '日期') or contains(@placeholder, 'date')]")
        print(f"   找到 {len(date_inputs)} 個日期輸入框")
        
        # 尋找select元素
        selects = driver.find_elements(By.TAG_NAME, "select")
        print(f"   找到 {len(selects)} 個選擇器:")
        for i, sel in enumerate(selects):
            try:
                options = sel.find_elements(By.TAG_NAME, "option")
                if options:
                    print(f"     選擇器 {i+1}: {len(options)} 個選項")
                    # 顯示前幾個選項
                    for j, opt in enumerate(options[:3]):
                        print(f"       - {opt.text.strip()}")
            except:
                continue
        
        # 7. 嘗試設置日期範圍（如果有的話）
        print("7. 嘗試設置日期範圍...")
        try:
            # 設置過去一個月的日期範圍
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30)
            
            # 尋找開始日期輸入框
            start_date_inputs = driver.find_elements(By.XPATH, "//input[contains(@id, 'start') or contains(@name, 'start') or contains(@placeholder, '開始')]")
            if start_date_inputs:
                start_input = start_date_inputs[0]
                start_input.clear()
                start_input.send_keys(start_date.strftime('%Y-%m-%d'))
                print(f"   ✓ 設置開始日期: {start_date.strftime('%Y-%m-%d')}")
            
            # 尋找結束日期輸入框
            end_date_inputs = driver.find_elements(By.XPATH, "//input[contains(@id, 'end') or contains(@name, 'end') or contains(@placeholder, '結束')]")
            if end_date_inputs:
                end_input = end_date_inputs[0]
                end_input.clear()
                end_input.send_keys(end_date.strftime('%Y-%m-%d'))
                print(f"   ✓ 設置結束日期: {end_date.strftime('%Y-%m-%d')}")
                
        except Exception as e:
            print(f"   ✗ 設置日期失敗: {e}")
        
        # 8. 尋找並點擊查詢/下載按鈕
        print("8. 尋找並點擊查詢/下載按鈕...")
        query_keywords = ['查詢', '搜尋', 'search', 'query', '下載', 'download', 'CSV', 'csv']
        
        for keyword in query_keywords:
            try:
                # 嘗試按鈕
                btn = driver.find_element(By.XPATH, f"//button[contains(text(), '{keyword}')]")
                driver.execute_script("arguments[0].click();", btn)
                print(f"   ✓ 點擊了包含 '{keyword}' 的按鈕")
                time.sleep(5)
                break
            except:
                try:
                    # 嘗試連結
                    link = driver.find_element(By.XPATH, f"//a[contains(text(), '{keyword}')]")
                    driver.execute_script("arguments[0].click();", link)
                    print(f"   ✓ 點擊了包含 '{keyword}' 的連結")
                    time.sleep(5)
                    break
                except:
                    continue
        
        # 9. 檢查下載結果
        print("9. 檢查下載結果...")
        time.sleep(10)  # 等待下載完成
        
        output_dir = "agricultural_data"
        if os.path.exists(output_dir):
            files = os.listdir(output_dir)
            csv_files = [f for f in files if f.endswith('.csv')]
            if csv_files:
                print(f"   ✓ 成功下載 {len(csv_files)} 個CSV檔案:")
                for f in csv_files:
                    file_path = os.path.join(output_dir, f)
                    file_size = os.path.getsize(file_path)
                    print(f"     - {f} ({file_size} bytes)")
                return True
            else:
                print("   ✗ 未找到CSV檔案")
        
        print("\n請手動檢查瀏覽器視窗，看看是否有其他操作步驟...")
        input("按Enter鍵繼續...")
        
        return False
        
    except Exception as e:
        print(f"測試失敗: {e}")
        return False
    finally:
        driver.quit()


if __name__ == "__main__":
    test_download_flow()
