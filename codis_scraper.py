#!/usr/bin/env python3
"""
CODiS 中央氣象署農業站月報表資料爬蟲
從 https://codis.cwa.gov.tw/StationData 爬取過去3年的農業站月報表CSV資料

使用方法：
1. 確保已安裝 Chrome 瀏覽器和 ChromeDriver
2. 安裝依賴：pip install selenium
3. 運行：python codis_scraper.py
"""

import os
import time
from datetime import datetime, timedelta
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import NoSuchElementException


def setup_chrome_driver(headless=True):
    """設置Chrome WebDriver"""
    print("正在設置Chrome WebDriver...")

    chrome_options = Options()
    if headless:
        chrome_options.add_argument("--headless")
        print("使用無頭模式")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option("useAutomationExtension", False)

    # 設置下載目錄
    output_dir = "agricultural_data"
    os.makedirs(output_dir, exist_ok=True)

    prefs = {
        "download.default_directory": os.path.abspath(output_dir),
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True,
    }
    chrome_options.add_experimental_option("prefs", prefs)

    try:
        # 新版本Selenium使用Service
        from selenium.webdriver.chrome.service import Service

        if os.path.exists("chromedriver.exe"):
            service = Service(executable_path="./chromedriver.exe")
            driver = webdriver.Chrome(service=service, options=chrome_options)
        else:
            driver = webdriver.Chrome(options=chrome_options)

        driver.implicitly_wait(10)
        # 隱藏自動化標識
        driver.execute_script(
            "Object.defineProperty(navigator, 'webdriver', {get: () => undefined})"
        )
        print("Chrome WebDriver 設置成功")
        return driver
    except Exception as e:
        print(f"Chrome WebDriver 設置失敗: {e}")
        print("請確保已安裝 ChromeDriver 並在 PATH 中")
        return None


def navigate_to_codis(driver):
    """導航到CODiS網站"""
    print("正在訪問CODiS網站...")
    try:
        driver.get("https://codis.cwa.gov.tw/StationData")

        # 等待頁面載入
        WebDriverWait(driver, 30).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        time.sleep(5)  # 等待JavaScript載入

        # 檢查頁面標題
        print(f"頁面標題: {driver.title}")
        print("CODiS網站載入成功")
        return True
    except Exception as e:
        print(f"訪問CODiS網站失敗: {e}")
        return False


def setup_station_filters(driver):
    """設置測站篩選條件"""
    print("正在設置測站篩選條件...")

    # 先等待頁面完全載入
    time.sleep(3)

    try:
        # 1. 取消勾選「署屬有人站」(cwb)
        print("1. 取消勾選「署屬有人站」...")
        cwb_checkbox = driver.find_element(By.ID, "cwb")
        if cwb_checkbox.is_selected():
            driver.execute_script("arguments[0].click();", cwb_checkbox)
            time.sleep(1)
            print("   ✓ 已取消勾選「署屬有人站」")
        else:
            print("   ✓ 「署屬有人站」已經未勾選")

        # 2. 確保勾選「農業站」(agr)
        print("2. 確保勾選「農業站」...")
        agr_checkbox = driver.find_element(By.ID, "agr")
        if not agr_checkbox.is_selected():
            driver.execute_script("arguments[0].click();", agr_checkbox)
            time.sleep(1)
            print("   ✓ 已勾選「農業站」")
        else:
            print("   ✓ 「農業站」已經勾選")

        # 3. 也可以取消其他不需要的站點類型
        print("3. 取消其他站點類型...")
        other_stations = ["auto_C1", "auto_C0"]  # 自動站
        for station_id in other_stations:
            try:
                checkbox = driver.find_element(By.ID, station_id)
                if checkbox.is_selected():
                    driver.execute_script("arguments[0].click();", checkbox)
                    time.sleep(1)
                    print(f"   ✓ 已取消勾選「{station_id}」")
            except:
                continue

        time.sleep(3)  # 等待地圖更新
        print("✓ 測站篩選條件設置完成")
        return True

    except Exception as e:
        print(f"✗ 設置測站篩選條件失敗: {e}")
        return False


def click_station_on_map(driver):
    """點擊地圖上的農業站點（綠色圈圈）"""
    print("   正在尋找地圖上的農業站標記...")

    time.sleep(5)  # 等待地圖和農業站標記載入

    try:
        # 專門尋找農業站的綠色標記
        # 農業站通常顯示為綠色圓圈
        station_selectors = [
            # SVG圓圈，綠色填充
            "//*[name()='circle' and contains(@fill, 'green')]",
            "//*[name()='circle' and contains(@fill, '#00ff00')]",
            "//*[name()='circle' and contains(@fill, '#008000')]",
            # 一般的圓圈標記
            "//circle[@r and @cx and @cy and @fill]",
            # 可能的標記容器
            "//div[contains(@class, 'marker') and contains(@style, 'green')]",
            "//div[contains(@class, 'station') and contains(@style, 'green')]",
            # 圖片標記
            "//img[contains(@src, 'green') or contains(@src, 'agr')]",
        ]

        print(f"   等待農業站標記出現...")

        for attempt in range(3):  # 嘗試3次
            stations_found = []

            for i, selector in enumerate(station_selectors):
                try:
                    elements = driver.find_elements(By.XPATH, selector)
                    if elements:
                        print(f"   找到 {len(elements)} 個標記 (選擇器 {i+1})")
                        stations_found.extend(elements)
                except:
                    continue

            if stations_found:
                # 嘗試點擊找到的站點
                for i, station in enumerate(stations_found[:3]):  # 只嘗試前3個
                    try:
                        print(f"   嘗試點擊農業站標記 {i+1}...")

                        # 確保元素可見
                        driver.execute_script(
                            "arguments[0].scrollIntoView(true);", station
                        )
                        time.sleep(1)

                        # 點擊站點
                        driver.execute_script("arguments[0].click();", station)
                        time.sleep(3)

                        # 檢查是否出現站點資訊面板
                        if check_station_info_panel(driver):
                            print(f"   ✓ 成功點擊農業站 {i+1}，出現站點資訊面板")
                            return True

                    except Exception as e:
                        print(f"   ✗ 點擊農業站 {i+1} 失敗: {e}")
                        continue

                print(f"   第 {attempt + 1} 次嘗試未成功，等待重試...")
                time.sleep(3)
            else:
                print(f"   第 {attempt + 1} 次未找到農業站標記，等待重試...")
                time.sleep(3)

        print("   ✗ 無法找到或點擊農業站標記")
        return False

    except Exception as e:
        print(f"   ✗ 點擊地圖站點失敗: {e}")
        return False


def check_station_info_panel(driver):
    """檢查是否出現站點資訊面板"""
    try:
        # 尋找可能的站點資訊面板
        panel_selectors = [
            "//div[contains(@class, 'popup') or contains(@class, 'info')]",
            "//div[contains(@class, 'modal') or contains(@class, 'dialog')]",
            "//div[contains(text(), '月報表') or contains(text(), 'CSV')]",
            "//select[contains(@id, 'report') or contains(@name, 'report')]",
        ]

        for selector in panel_selectors:
            elements = driver.find_elements(By.XPATH, selector)
            if elements:
                return True

        return False
    except:
        return False


def select_monthly_report_and_date(driver, year, month):
    """選擇月報表並設置日期"""
    print(f"正在選擇月報表並設置日期 {year}/{month:02d}...")

    try:
        # 1. 尋找月報表選項
        print("1. 尋找月報表選項...")
        monthly_report_selectors = [
            "//input[@type='radio' and contains(@value, '月報表')]",
            "//label[contains(text(), '月報表')]//input",
            "//option[contains(text(), '月報表')]",
            "//select//option[contains(text(), '月報表')]",
            "//input[@value='monthly' or @value='month']",
        ]

        for selector in monthly_report_selectors:
            try:
                element = driver.find_element(By.XPATH, selector)
                if element.tag_name == "option":
                    select_element = element.find_element(By.XPATH, "./..")
                    select = Select(select_element)
                    select.select_by_visible_text("月報表")
                    print("   ✓ 已選擇月報表 (下拉選單)")
                else:
                    driver.execute_script("arguments[0].click();", element)
                    print("   ✓ 已選擇月報表 (單選按鈕)")

                time.sleep(2)
                break
            except:
                continue

        # 2. 設置年份
        print("2. 設置年份...")
        year_selectors = [
            f"//select//option[@value='{year}']",
            f"//select//option[text()='{year}']",
            "//select[contains(@id, 'year') or contains(@name, 'year')]",
            "//input[contains(@id, 'year') or contains(@name, 'year')]",
        ]

        for selector in year_selectors:
            try:
                if "option" in selector:
                    option = driver.find_element(By.XPATH, selector)
                    select_element = option.find_element(By.XPATH, "./..")
                    select = Select(select_element)
                    select.select_by_value(str(year))
                    print(f"   ✓ 已設置年份: {year}")
                    break
                else:
                    input_element = driver.find_element(By.XPATH, selector)
                    input_element.clear()
                    input_element.send_keys(str(year))
                    print(f"   ✓ 已輸入年份: {year}")
                    break
            except:
                continue

        # 3. 設置月份
        print("3. 設置月份...")
        month_selectors = [
            f"//select//option[@value='{month:02d}']",
            f"//select//option[@value='{month}']",
            f"//select//option[text()='{month:02d}']",
            f"//select//option[text()='{month}']",
            "//select[contains(@id, 'month') or contains(@name, 'month')]",
            "//input[contains(@id, 'month') or contains(@name, 'month')]",
        ]

        for selector in month_selectors:
            try:
                if "option" in selector:
                    option = driver.find_element(By.XPATH, selector)
                    select_element = option.find_element(By.XPATH, "./..")
                    select = Select(select_element)
                    try:
                        select.select_by_value(f"{month:02d}")
                    except:
                        select.select_by_value(str(month))
                    print(f"   ✓ 已設置月份: {month:02d}")
                    break
                else:
                    input_element = driver.find_element(By.XPATH, selector)
                    input_element.clear()
                    input_element.send_keys(f"{month:02d}")
                    print(f"   ✓ 已輸入月份: {month:02d}")
                    break
            except:
                continue

        time.sleep(2)
        print("✓ 月報表和日期設置完成")
        return True

    except Exception as e:
        print(f"✗ 選擇月報表和設置日期失敗: {e}")
        return False


def set_date_range(driver, years=3):
    """設置日期範圍"""
    print(f"正在設置過去{years}年的日期範圍...")

    end_date = datetime.now()
    start_date = end_date - timedelta(days=years * 365)

    try:
        # 嘗試設置年月選擇器
        start_year_select = Select(driver.find_element(By.NAME, "startYear"))
        start_year_select.select_by_value(str(start_date.year))

        start_month_select = Select(driver.find_element(By.NAME, "startMonth"))
        start_month_select.select_by_value(str(start_date.month))

        end_year_select = Select(driver.find_element(By.NAME, "endYear"))
        end_year_select.select_by_value(str(end_date.year))

        end_month_select = Select(driver.find_element(By.NAME, "endMonth"))
        end_month_select.select_by_value(str(end_date.month))

        time.sleep(1)
        print(
            f"日期範圍設置成功: {start_date.strftime('%Y/%m')} - {end_date.strftime('%Y/%m')}"
        )
        return True

    except NoSuchElementException:
        print("警告：未找到日期選擇器，可能需要手動設置")
        return True


def download_csv_data(driver):
    """下載CSV資料"""
    print("正在嘗試下載CSV資料...")

    # 記錄下載前的檔案數量
    output_dir = "agricultural_data"
    before_files = set(os.listdir(output_dir))

    # 更全面的CSV下載按鈕選擇器
    selectors = [
        "//button[contains(text(), 'CSV')]",
        "//a[contains(text(), 'CSV')]",
        "//input[@value='CSV']",
        "//button[contains(@class, 'csv')]",
        "//a[contains(@class, 'download')]",
        "//button[contains(text(), '下載')]",
        "//a[contains(text(), '下載')]",
        "//button[contains(text(), '匯出')]",
        "//a[contains(text(), '匯出')]",
        "//input[@type='button' and contains(@value, 'CSV')]",
        "//input[@type='submit' and contains(@value, 'CSV')]",
        "//span[contains(text(), 'CSV')]//parent::button",
        "//span[contains(text(), '下載')]//parent::button",
    ]

    print("尋找CSV下載按鈕...")

    for i, selector in enumerate(selectors):
        try:
            print(f"   嘗試選擇器 {i+1}: {selector}")
            download_button = driver.find_element(By.XPATH, selector)

            # 檢查按鈕是否可見和可點擊
            if download_button.is_displayed() and download_button.is_enabled():
                print(f"   找到可用的下載按鈕: {download_button.text.strip()}")
                driver.execute_script("arguments[0].click();", download_button)
                print("   ✓ 點擊下載按鈕成功")
                time.sleep(10)  # 等待下載完成

                # 檢查是否有新檔案
                after_files = set(os.listdir(output_dir))
                new_files = after_files - before_files
                csv_files = [f for f in new_files if f.endswith(".csv")]

                if csv_files:
                    print(f"   ✓ 下載成功！新增 {len(csv_files)} 個CSV檔案:")
                    for file in csv_files:
                        file_path = os.path.join(output_dir, file)
                        file_size = os.path.getsize(file_path)
                        print(f"     - {file} ({file_size} bytes)")
                    return True
                else:
                    print("   ✗ 未檢測到新的CSV檔案，繼續嘗試其他按鈕...")
            else:
                print(
                    f"   按鈕不可用 (displayed: {download_button.is_displayed()}, enabled: {download_button.is_enabled()})"
                )

        except NoSuchElementException:
            print(f"   選擇器 {i+1} 未找到元素")
            continue
        except Exception as e:
            print(f"   選擇器 {i+1} 發生錯誤: {e}")
            continue

    print("✗ 未找到可用的CSV下載按鈕")
    return False


def scrape_agricultural_data_by_month(year, month, headless=True):
    """按月份爬取農業站資料"""
    print("=" * 60)
    print(f"CODiS 農業站月報表資料爬蟲 - {year}/{month:02d}")
    print("=" * 60)

    # 設置WebDriver
    driver = setup_chrome_driver(headless)
    if not driver:
        return False

    try:
        # 1. 導航到網站
        print("步驟 1: 導航到CODiS網站")
        if not navigate_to_codis(driver):
            return False

        # 2. 設置測站篩選條件 (取消署屬有人站，保持農業站)
        print("步驟 2: 設置測站篩選條件")
        if not setup_station_filters(driver):
            return False

        # 3. 點擊地圖上的農業站點（綠色圈圈）
        print("步驟 3: 點擊地圖上的農業站點（綠色圈圈）")
        print("   農業站勾選後，地圖上會顯示綠色圈圈")
        if not click_station_on_map(driver):
            print("   警告：無法自動點擊地圖站點")
            print("   請手動點擊地圖上的綠色農業站圈圈")
            input("   點擊後會彈出站點資訊，然後按Enter繼續...")

        # 4. 選擇月報表並設置日期
        print("步驟 4: 選擇月報表並設置日期")
        if not select_monthly_report_and_date(driver, year, month):
            print("   警告：無法自動設置月報表和日期")
            input("   請手動選擇月報表並設置日期，然後按Enter繼續...")

        # 5. 下載CSV資料
        print("步驟 5: 下載CSV資料")
        if not download_csv_data(driver):
            print("\n自動下載失敗，請手動操作：")
            print("1. 確認已選擇月報表")
            print(f"2. 確認日期設置為 {year}/{month:02d}")
            print("3. 點擊CSV下載按鈕")
            input("請完成手動下載後按Enter繼續...")

        print(f"\n✓ {year}/{month:02d} 月份資料爬取完成！")
        return True

    except Exception as e:
        print(f"✗ 爬取過程中發生錯誤: {e}")
        return False
    finally:
        if not headless:
            input("按Enter鍵關閉瀏覽器...")
        driver.quit()


def scrape_agricultural_data(years=3, headless=True):
    """主要爬取函數 - 爬取過去指定年數的資料"""
    print("=" * 50)
    print("CODiS 農業站月報表資料爬蟲")
    print("=" * 50)

    # 計算要爬取的月份
    end_date = datetime.now()
    start_date = end_date - timedelta(days=years * 365)

    print(
        f"準備爬取 {start_date.strftime('%Y/%m')} 到 {end_date.strftime('%Y/%m')} 的資料"
    )

    success_count = 0
    total_months = 0

    # 按月份逐一爬取
    current_date = start_date.replace(day=1)  # 從月初開始
    while current_date <= end_date:
        total_months += 1
        print(f"\n處理第 {total_months} 個月份: {current_date.strftime('%Y/%m')}")

        if scrape_agricultural_data_by_month(
            current_date.year, current_date.month, headless
        ):
            success_count += 1
            print(f"✓ {current_date.strftime('%Y/%m')} 成功")
        else:
            print(f"✗ {current_date.strftime('%Y/%m')} 失敗")

        # 移到下個月
        if current_date.month == 12:
            current_date = current_date.replace(year=current_date.year + 1, month=1)
        else:
            current_date = current_date.replace(month=current_date.month + 1)

        time.sleep(2)  # 避免請求過於頻繁

    print(f"\n爬取完成！成功: {success_count}/{total_months}")
    print(f"資料已儲存到 agricultural_data/ 目錄")

    return success_count > 0


def main():
    """主函數"""
    try:
        # 詢問是否使用無頭模式
        headless_input = input("是否使用無頭模式？(y/n，預設y): ").strip().lower()
        headless = headless_input != "n"

        # 開始爬取
        success = scrape_agricultural_data(years=3, headless=headless)

        if success:
            print("\n✓ 爬取成功完成！")
        else:
            print("\n✗ 爬取失敗，請檢查網路連接或手動下載")

    except KeyboardInterrupt:
        print("\n程式被使用者中斷")
    except Exception as e:
        print(f"\n程式執行失敗: {e}")


if __name__ == "__main__":
    main()
