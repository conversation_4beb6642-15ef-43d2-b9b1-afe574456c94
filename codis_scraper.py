#!/usr/bin/env python3
"""
CODiS 中央氣象署農業站月報表資料爬蟲
從 https://codis.cwa.gov.tw/StationData 爬取過去3年的農業站月報表CSV資料

使用方法：
1. 確保已安裝 Chrome 瀏覽器和 ChromeDriver
2. 安裝依賴：pip install selenium
3. 運行：python codis_scraper.py
"""

import os
import time
from datetime import datetime, timedelta
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import NoSuchElementException


def setup_chrome_driver(headless=True):
    """設置Chrome WebDriver"""
    print("正在設置Chrome WebDriver...")

    chrome_options = Options()
    if headless:
        chrome_options.add_argument("--headless")
        print("使用無頭模式")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option("useAutomationExtension", False)

    # 設置下載目錄
    output_dir = "agricultural_data"
    os.makedirs(output_dir, exist_ok=True)

    prefs = {
        "download.default_directory": os.path.abspath(output_dir),
        "download.prompt_for_download": False,
        "download.directory_upgrade": True,
        "safebrowsing.enabled": True,
    }
    chrome_options.add_experimental_option("prefs", prefs)

    try:
        # 新版本Selenium使用Service
        from selenium.webdriver.chrome.service import Service

        if os.path.exists("chromedriver.exe"):
            service = Service(executable_path="./chromedriver.exe")
            driver = webdriver.Chrome(service=service, options=chrome_options)
        else:
            driver = webdriver.Chrome(options=chrome_options)

        driver.implicitly_wait(10)
        # 隱藏自動化標識
        driver.execute_script(
            "Object.defineProperty(navigator, 'webdriver', {get: () => undefined})"
        )
        print("Chrome WebDriver 設置成功")
        return driver
    except Exception as e:
        print(f"Chrome WebDriver 設置失敗: {e}")
        print("請確保已安裝 ChromeDriver 並在 PATH 中")
        return None


def navigate_to_codis(driver):
    """導航到CODiS網站"""
    print("正在訪問CODiS網站...")
    try:
        driver.get("https://codis.cwa.gov.tw/StationData")

        # 等待頁面載入
        WebDriverWait(driver, 30).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        time.sleep(5)  # 等待JavaScript載入

        # 檢查頁面標題
        print(f"頁面標題: {driver.title}")
        print("CODiS網站載入成功")
        return True
    except Exception as e:
        print(f"訪問CODiS網站失敗: {e}")
        return False


def select_agricultural_stations(driver):
    """選擇農業站"""
    print("正在選擇農業站...")

    # 先等待頁面完全載入
    time.sleep(3)

    # 嘗試多種可能的農業站選擇方式
    selectors = [
        "//input[@type='checkbox' and contains(@value, '農業')]",
        "//input[@type='checkbox' and contains(@id, 'agricultural')]",
        "//label[contains(text(), '農業站')]//input",
        "//span[contains(text(), '農業站')]//input",
        "//div[contains(@class, 'station-type')]//input[contains(@value, '農業')]",
        "//input[contains(@name, 'station') and contains(@value, '農業')]",
        "//input[@type='checkbox'][contains(@onclick, '農業')]",
    ]

    print("嘗試尋找農業站選項...")

    for i, selector in enumerate(selectors):
        try:
            print(f"嘗試選擇器 {i+1}: {selector}")
            checkbox = driver.find_element(By.XPATH, selector)
            print(f"找到元素: {checkbox.tag_name}, 已選擇: {checkbox.is_selected()}")

            if not checkbox.is_selected():
                driver.execute_script("arguments[0].click();", checkbox)
                time.sleep(2)
                print("農業站已選擇")
                return True
            else:
                print("農業站已經被選擇")
                return True
        except NoSuchElementException:
            print(f"選擇器 {i+1} 未找到元素")
            continue
        except Exception as e:
            print(f"選擇器 {i+1} 發生錯誤: {e}")
            continue

    # 如果都找不到，嘗試列出所有checkbox
    try:
        checkboxes = driver.find_elements(By.XPATH, "//input[@type='checkbox']")
        print(f"頁面上找到 {len(checkboxes)} 個checkbox:")
        for i, cb in enumerate(checkboxes[:10]):  # 只顯示前10個
            try:
                value = cb.get_attribute("value") or ""
                id_attr = cb.get_attribute("id") or ""
                name_attr = cb.get_attribute("name") or ""
                print(f"  {i+1}. value='{value}', id='{id_attr}', name='{name_attr}'")
            except:
                pass
    except:
        pass

    print("警告：未找到農業站選項，繼續執行...")
    return True


def select_monthly_report(driver):
    """選擇月報表"""
    print("正在選擇月報表...")

    selectors = [
        "//input[@type='radio' and contains(@value, '月報表')]",
        "//label[contains(text(), '月報表')]//input",
        "//option[contains(text(), '月報表')]",
    ]

    for selector in selectors:
        try:
            element = driver.find_element(By.XPATH, selector)
            if element.tag_name == "option":
                select_element = element.find_element(By.XPATH, "./..")
                select = Select(select_element)
                select.select_by_visible_text("月報表")
            else:
                driver.execute_script("arguments[0].click();", element)

            time.sleep(1)
            print("月報表已選擇")
            return True
        except NoSuchElementException:
            continue

    print("警告：未找到月報表選項，繼續執行...")
    return True


def set_date_range(driver, years=3):
    """設置日期範圍"""
    print(f"正在設置過去{years}年的日期範圍...")

    end_date = datetime.now()
    start_date = end_date - timedelta(days=years * 365)

    try:
        # 嘗試設置年月選擇器
        start_year_select = Select(driver.find_element(By.NAME, "startYear"))
        start_year_select.select_by_value(str(start_date.year))

        start_month_select = Select(driver.find_element(By.NAME, "startMonth"))
        start_month_select.select_by_value(str(start_date.month))

        end_year_select = Select(driver.find_element(By.NAME, "endYear"))
        end_year_select.select_by_value(str(end_date.year))

        end_month_select = Select(driver.find_element(By.NAME, "endMonth"))
        end_month_select.select_by_value(str(end_date.month))

        time.sleep(1)
        print(
            f"日期範圍設置成功: {start_date.strftime('%Y/%m')} - {end_date.strftime('%Y/%m')}"
        )
        return True

    except NoSuchElementException:
        print("警告：未找到日期選擇器，可能需要手動設置")
        return True


def download_csv_data(driver):
    """下載CSV資料"""
    print("正在嘗試下載CSV資料...")

    # 記錄下載前的檔案數量
    output_dir = "agricultural_data"
    before_files = set(os.listdir(output_dir))

    selectors = [
        "//button[contains(text(), 'CSV')]",
        "//a[contains(text(), 'CSV')]",
        "//input[@value='CSV']",
        "//button[contains(@class, 'csv')]",
        "//a[contains(@class, 'download')]",
        "//button[contains(text(), '下載')]",
    ]

    for selector in selectors:
        try:
            download_button = driver.find_element(By.XPATH, selector)
            driver.execute_script("arguments[0].click();", download_button)
            print("點擊下載按鈕成功")
            time.sleep(10)  # 等待下載完成

            # 檢查是否有新檔案
            after_files = set(os.listdir(output_dir))
            new_files = after_files - before_files
            csv_files = [f for f in new_files if f.endswith(".csv")]

            if csv_files:
                print(f"下載成功！新增 {len(csv_files)} 個CSV檔案:")
                for file in csv_files:
                    print(f"  - {file}")
                return True
            else:
                print("未檢測到新的CSV檔案")

        except NoSuchElementException:
            continue

    print("警告：未找到CSV下載按鈕")
    return False


def scrape_agricultural_data(years=3, headless=True):
    """主要爬取函數"""
    print("=" * 50)
    print("CODiS 農業站月報表資料爬蟲")
    print("=" * 50)

    # 設置WebDriver
    driver = setup_chrome_driver(headless)
    if not driver:
        return False

    try:
        # 1. 導航到網站
        if not navigate_to_codis(driver):
            return False

        # 2. 選擇農業站
        if not select_agricultural_stations(driver):
            return False

        # 3. 選擇月報表
        if not select_monthly_report(driver):
            return False

        # 4. 設置日期範圍
        if not set_date_range(driver, years):
            return False

        # 5. 下載CSV資料
        if not download_csv_data(driver):
            print("\n自動下載失敗，請手動操作：")
            print("1. 在瀏覽器中確認已選擇農業站和月報表")
            print("2. 設置正確的日期範圍")
            print("3. 點擊CSV下載按鈕")
            return False

        print("\n爬取完成！")
        print(f"資料已儲存到 agricultural_data/ 目錄")
        return True

    except Exception as e:
        print(f"爬取過程中發生錯誤: {e}")
        return False
    finally:
        driver.quit()


def main():
    """主函數"""
    try:
        # 詢問是否使用無頭模式
        headless_input = input("是否使用無頭模式？(y/n，預設y): ").strip().lower()
        headless = headless_input != "n"

        # 開始爬取
        success = scrape_agricultural_data(years=3, headless=headless)

        if success:
            print("\n✓ 爬取成功完成！")
        else:
            print("\n✗ 爬取失敗，請檢查網路連接或手動下載")

    except KeyboardInterrupt:
        print("\n程式被使用者中斷")
    except Exception as e:
        print(f"\n程式執行失敗: {e}")


if __name__ == "__main__":
    main()
