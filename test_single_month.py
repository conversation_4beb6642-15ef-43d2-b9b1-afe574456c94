#!/usr/bin/env python3
"""
測試單個月份的爬取功能
"""

from codis_scraper import scrape_agricultural_data_by_month
from datetime import datetime

def test_single_month():
    """測試爬取單個月份的資料"""
    # 測試爬取上個月的資料
    current_date = datetime.now()
    
    # 計算上個月
    if current_date.month == 1:
        test_year = current_date.year - 1
        test_month = 12
    else:
        test_year = current_date.year
        test_month = current_date.month - 1
    
    print(f"測試爬取 {test_year}/{test_month:02d} 的農業站月報表資料")
    print("=" * 50)
    
    # 使用有頭模式方便觀察和手動操作
    success = scrape_agricultural_data_by_month(test_year, test_month, headless=False)
    
    if success:
        print(f"\n✓ 測試成功！{test_year}/{test_month:02d} 資料爬取完成")
    else:
        print(f"\n✗ 測試失敗！{test_year}/{test_month:02d} 資料爬取失敗")
    
    return success

if __name__ == "__main__":
    test_single_month()
